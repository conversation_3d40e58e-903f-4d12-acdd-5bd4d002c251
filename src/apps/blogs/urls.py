from django.urls import path
from .views.blog_views import (
    CreateBlogView,
    UpdateBlogView,
    GetAllBlogsView,
    GetBlogByIdView,
    GetBlogsByAuthorView,
    DeleteBlogView
)

app_name = 'blogs'

urlpatterns = [
    path('create/', CreateBlogView.as_view(), name='create_blog'),
    path('update/', UpdateBlogView.as_view(), name='update_blog'),
    path('delete/<uuid:id>/', DeleteBlogView.as_view(), name='delete_blog'),
    
    path('', GetAllBlogsView.as_view(), name='get_all_blogs'),
    path('<uuid:id>/', GetBlogByIdView.as_view(), name='get_blog_by_id'),
    path('author/<uuid:author_id>/', GetBlogsByAuthorView.as_view(), name='get_blogs_by_author'),
]

import logging
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import Q
from apps.users.exceptions.exceptions import (
    UserAlreadyExistsException,
    UserNotFoundException,
    InvalidUserDataException,
    FileUploadException,
    UserPasswordIsWrong
)
from common.services.file_service import FileService

User = get_user_model()
logger = logging.getLogger(__name__)
class UserService:

    @staticmethod
    def create_user(username,email,password):

        if User.objects.filter(username=username).exists():
            raise UserAlreadyExistsException(f"User with username '{username}' already exists")
        
        if User.objects.filter(email=email).exists():
            raise UserAlreadyExistsException(f"User with email '{email}' already exists")
        try:
            user=User.create_user(username,email,password)
            user.save()
            return user
        except Exception as e:
            logger.error(f"Failed to create user {username}: {str(e)}")
            raise InvalidUserDataException(f"Failed to create user: {str(e)}")




    @staticmethod
    def modify_user(user_id, first_name, last_name, username, email, image_modified, image):
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise UserNotFoundException(f"User with ID '{user_id}' not found")

        # Check if username already exists for other users
        if User.objects.filter(~Q(id=user_id), username=username).exists():
            logger.warning(f"Attempt to modify user {user_id} with existing username: {username}")
            raise UserAlreadyExistsException(f"Username '{username}' already exists")

        # Check if email already exists for other users
        if User.objects.filter(~Q(id=user_id), email=email).exists():
            logger.warning(f"Attempt to modify user {user_id} with existing email: {email}")
            raise UserAlreadyExistsException(f"Email '{email}' already exists")
        new_image_url = None
        if image_modified:
            try:
                new_image_url = FileService.update(user.image_url, image, "storage/public")
            except Exception as e:
                logger.error(f"Image upload failed for user {username}: {str(e)}")
                raise FileUploadException(f"Failed to upload image: {str(e)}")

        try:
            # Update user fields directly
            user.first_name = first_name
            user.last_name = last_name
            user.username = username
            user.email = email
            if new_image_url:
                user.image_url = new_image_url

            user.save()
            logger.info(f"User {user_id} modified successfully")
            return user

        except Exception as e:
            logger.error(f"Failed to modify user {user_id}: {str(e)}")
            raise InvalidUserDataException(f"Failed to modify user: {str(e)}")
    

    @staticmethod
    def modify_password(user_id,old_password,new_password):
        user=User.objects.get(id=user_id)
        if user is None:
            raise UserNotFoundException("User not found")
        if user.check_password(old_password):
            raise UserPasswordIsWrong
        user.set_password(new_password)
        user.save()

    @staticmethod
    def get_all_users(include_inactive=False):
        try:
            if include_inactive:
                users = User.objects.all().order_by('-date_joined')
            else:
                users = User.objects.filter(is_active=True).order_by('-date_joined')

            return users

        except Exception as e:
            raise InvalidUserDataException(f"Failed to retrieve users: {str(e)}")

    @staticmethod
    def get_user_by_id(user_id):
        try:
            user = User.objects.get(id=user_id)
            return user
        except User.DoesNotExist:
            raise UserNotFoundException(f"User with ID '{user_id}' not found")
        except Exception as e:
            raise InvalidUserDataException(f"Failed to retrieve user: {str(e)}")
    
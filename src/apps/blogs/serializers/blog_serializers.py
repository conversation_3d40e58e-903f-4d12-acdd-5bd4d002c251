import logging
from rest_framework import serializers
from django.contrib.auth import get_user_model
from ..models.models import Blog
from ..services.blog_service import BlogService

User = get_user_model()
logger = logging.getLogger(__name__)

class BlogCreateSerializer(serializers.ModelSerializer):
    author_id = serializers.UUIDField(write_only=True, help_text="ID of the blog author")
    image = serializers.ImageField(required=False, allow_null=True, help_text="Blog image (optional)")

    class Meta:
        model = Blog
        fields = ['title', 'content', 'author_id', 'image']
        extra_kwargs = {
            'title': {'required': True, 'allow_blank': False},
            'content': {'required': True, 'allow_blank': False},
        }

    def create(self, validated_data):
        author_id = validated_data.pop('author_id')
        image = validated_data.pop('image', None)
        
        blog = BlogService.create_blog(
            title=validated_data['title'],
            content=validated_data['content'],
            author_id=author_id,
            image=image
        )
        return blog

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'title': instance.title,
            'content': instance.content,
            'author': {
                'id': instance.author.id,
                'username': instance.author.username,
                'full_name': instance.author.full_name
            },
            'image_url': instance.image_url,
            'date_created': instance.date_created,
            'date_updated': instance.date_updated
        }

class BlogUpdateSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(required=True)
    image = serializers.ImageField(required=False, allow_null=True, help_text="Blog image (optional)")
    image_modified = serializers.BooleanField(write_only=True, required=False, default=False)

    class Meta:
        model = Blog
        fields = ['id', 'title', 'content', 'image', 'image_modified']
        extra_kwargs = {
            'title': {'required': True, 'allow_blank': False},
            'content': {'required': True, 'allow_blank': False},
        }

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'title': instance.title,
            'content': instance.content,
            'author': {
                'id': instance.author.id,
                'username': instance.author.username,
                'full_name': instance.author.full_name
            },
            'image_url': instance.image_url,
            'date_created': instance.date_created,
            'date_updated': instance.date_updated
        }

class BlogListSerializer(serializers.ModelSerializer):
    author = serializers.SerializerMethodField()

    class Meta:
        model = Blog
        fields = ['id', 'title', 'content', 'author', 'image_url', 'date_created', 'date_updated']
        read_only_fields = ['id', 'date_created', 'date_updated']

    def get_author(self, obj):
        return {
            'id': obj.author.id,
            'username': obj.author.username,
            'full_name': obj.author.full_name,
            'image_url': obj.author.image_url
        }

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'title': instance.title,
            'content': instance.content[:200] + '...' if len(instance.content) > 200 else instance.content,
            'author': self.get_author(instance),
            'image_url': instance.image_url,
            'date_created': instance.date_created,
            'date_updated': instance.date_updated
        }

class BlogDetailSerializer(serializers.ModelSerializer):
    author = serializers.SerializerMethodField()

    class Meta:
        model = Blog
        fields = ['id', 'title', 'content', 'author', 'image_url', 'date_created', 'date_updated']
        read_only_fields = ['id', 'date_created', 'date_updated']

    def get_author(self, obj):
        return {
            'id': obj.author.id,
            'username': obj.author.username,
            'full_name': obj.author.full_name,
            'image_url': obj.author.image_url
        }

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'title': instance.title,
            'content': instance.content,
            'author': self.get_author(instance),
            'image_url': instance.image_url,
            'date_created': instance.date_created,
            'date_updated': instance.date_updated
        }

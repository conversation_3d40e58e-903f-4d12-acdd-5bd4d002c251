import json
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

User = get_user_model()


class GetAllUsersTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse('get_all_users')
        
        # Create test users
        self.user1 = User.objects.create_user(
            username='testuser1',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User1'
        )
        
        self.user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User2'
        )
        
        # Create an inactive user
        self.inactive_user = User.objects.create_user(
            username='inactiveuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Inactive',
            last_name='User',
            is_active=False
        )

    def test_get_all_active_users(self):
        """Test getting all active users"""
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('count', data)
        self.assertIn('users', data)
        
        # Should return 2 active users (excluding inactive user)
        self.assertEqual(data['count'], 2)
        self.assertEqual(len(data['users']), 2)
        
        # Check user data structure
        user_data = data['users'][0]
        expected_fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'full_name', 'image_url', 'phone_number', 'date_joined', 'is_active'
        ]
        
        for field in expected_fields:
            self.assertIn(field, user_data)

    def test_get_all_users_including_inactive(self):
        """Test getting all users including inactive ones"""
        response = self.client.get(self.url, {'include_inactive': 'true'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        
        # Should return 3 users (including inactive user)
        self.assertEqual(data['count'], 3)
        self.assertEqual(len(data['users']), 3)

    def test_get_all_users_empty_database(self):
        """Test getting users when database is empty"""
        # Delete all users
        User.objects.all().delete()
        
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['count'], 0)
        self.assertEqual(len(data['users']), 0)

    def test_user_serialization_format(self):
        """Test that user data is properly serialized"""
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()
        user_data = data['users'][0]

        # Check that full_name is properly computed (could be either user due to ordering)
        self.assertIn(user_data['full_name'], ['Test User1', 'Test User2'])

        # Check that sensitive fields are not included
        self.assertNotIn('password', user_data)
        self.assertNotIn('date_deleted', user_data)

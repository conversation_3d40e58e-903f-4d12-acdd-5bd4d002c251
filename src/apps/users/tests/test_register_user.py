import uuid
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model

User = get_user_model()


class RegisterUserTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.register_url = reverse('register')
        
        self.valid_user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'password_confirm': 'testpassword123'
        }

    def test_register_user_success(self):
        """Test successful user registration"""
        response = self.client.post(self.register_url, self.valid_user_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check response structure
        data = response.json()
        self.assertIn('user_id', data)
        self.assertIn('username', data)
        self.assertIn('email', data)
        self.assertIn('access_token', data)
        self.assertIn('refresh_token', data)
        
        # Verify user was created in database
        self.assertTrue(User.objects.filter(username='testuser').exists())
        user = User.objects.get(username='testuser')
        self.assertEqual(user.email, '<EMAIL>')

    def test_register_user_duplicate_username(self):
        """Test registration with duplicate username"""
        # Create a user first
        User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='password123'
        )
        
        response = self.client.post(self.register_url, self.valid_user_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_409_CONFLICT)

        # Check error response format
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['success'], False)
        self.assertIn('USER_ALREADY_EXISTS', data['error']['code'])

    def test_register_user_duplicate_email(self):
        """Test registration with duplicate email"""
        # Create a user first
        User.objects.create_user(
            username='existinguser',
            email='<EMAIL>',
            password='password123'
        )
        
        user_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'password_confirm': 'testpassword123'
        }
        
        response = self.client.post(self.register_url, user_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_409_CONFLICT)

        # Check error response format
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['success'], False)
        self.assertIn('USER_ALREADY_EXISTS', data['error']['code'])

    def test_register_user_missing_fields(self):
        """Test registration with missing required fields"""
        incomplete_data = {
            'username': 'testuser'
            # Missing email and password
        }
        
        response = self.client.post(self.register_url, incomplete_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_register_user_invalid_email(self):
        """Test registration with invalid email format"""
        invalid_data = {
            'username': 'testuser',
            'email': 'invalid-email',
            'password': 'testpassword123',
            'password_confirm': 'testpassword123'
        }
        
        response = self.client.post(self.register_url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_register_user_empty_username(self):
        """Test registration with empty username"""
        invalid_data = {
            'username': '',
            'email': '<EMAIL>',
            'password': 'testpassword123',
            'password_confirm': 'testpassword123'
        }
        
        response = self.client.post(self.register_url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_register_user_empty_password(self):
        """Test registration with empty password"""
        invalid_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': '',
            'password_confirm': ''
        }
        
        response = self.client.post(self.register_url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_response_data_structure(self):
        """Test that response has the expected data structure"""
        response = self.client.post(self.register_url, self.valid_user_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        data = response.json()
        
        # Check required fields
        required_fields = ['user_id', 'username', 'email', 'access_token', 'refresh_token']
        for field in required_fields:
            self.assertIn(field, data)
            self.assertIsNotNone(data[field])
        
        # Check data types
        self.assertIsInstance(data['user_id'], str)
        self.assertIsInstance(data['username'], str)
        self.assertIsInstance(data['email'], str)
        self.assertIsInstance(data['access_token'], str)
        self.assertIsInstance(data['refresh_token'], str)
        
        # Verify UUID format
        try:
            uuid.UUID(data['user_id'])
        except ValueError:
            self.fail("user_id is not a valid UUID")

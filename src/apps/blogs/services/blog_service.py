import logging
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import Q
from ..models.models import Blog
from ..exceptions.exceptions import (
    BlogNotFoundException,
    InvalidBlogDataException,
    BlogPermissionDeniedException,
    FileUploadException
)
from common.services.file_service import FileService

User = get_user_model()
logger = logging.getLogger(__name__)

class BlogService:

    @staticmethod
    def create_blog(title, content, author_id, image=None):
        try:
            try:
                author = User.objects.get(id=author_id)
            except User.DoesNotExist:
                raise InvalidBlogDataException(f"Author with ID '{author_id}' not found")

            image_url = None
            if image:
                try:
                    image_url = FileService.upload(image, "storage/public/blogs")
                except Exception as e:
                    logger.error(f"Image upload failed for blog: {str(e)}")
                    raise FileUploadException(f"Failed to upload image: {str(e)}")

            blog = Blog(
                title=title,
                content=content,
                author=author,
                image_url=image_url
            )
            blog.save()
            
            logger.info(f"Blog created successfully: {blog.title} by {author.username}")
            return blog

        except Exception as e:
            if isinstance(e, (InvalidBlogDataException, FileUploadException)):
                raise e
            logger.error(f"Failed to create blog: {str(e)}")
            raise InvalidBlogDataException(f"Failed to create blog: {str(e)}")

    @staticmethod
    def update_blog(blog_id, title, content, image_modified=False, image=None, user_id=None):
        try:
            blog = Blog.objects.get(id=blog_id)
        except Blog.DoesNotExist:
            raise BlogNotFoundException(f"Blog with ID '{blog_id}' not found")

        if user_id and str(blog.author.id) != str(user_id):
            raise BlogPermissionDeniedException("You can only update your own blog posts")

        try:
            if image_modified:
                try:
                    new_image_url = FileService.update(blog.image_url, image, "storage/public/blogs")
                    blog.image_url = new_image_url
                except Exception as e:
                    logger.error(f"Image upload failed for blog {blog_id}: {str(e)}")
                    raise FileUploadException(f"Failed to upload image: {str(e)}")

            blog.title = title
            blog.content = content
            blog.date_updated = timezone.now()
            blog.save()

            logger.info(f"Blog updated successfully: {blog.title}")
            return blog

        except Exception as e:
            if isinstance(e, (BlogNotFoundException, BlogPermissionDeniedException, FileUploadException)):
                raise e
            logger.error(f"Failed to update blog {blog_id}: {str(e)}")
            raise InvalidBlogDataException(f"Failed to update blog: {str(e)}")

    @staticmethod
    def delete_blog(blog_id, user_id=None):
        try:
            blog = Blog.objects.get(id=blog_id)
        except Blog.DoesNotExist:
            raise BlogNotFoundException(f"Blog with ID '{blog_id}' not found")

        if user_id and str(blog.author.id) != str(user_id):
            raise BlogPermissionDeniedException("You can only delete your own blog posts")

        try:
            if blog.image_url:
                try:
                    FileService.delete(blog.image_url)
                except Exception as e:
                    logger.warning(f"Failed to delete image for blog {blog_id}: {str(e)}")

            blog.delete()
            logger.info(f"Blog deleted successfully: {blog.title}")
            return True

        except Exception as e:
            if isinstance(e, (BlogNotFoundException, BlogPermissionDeniedException)):
                raise e
            logger.error(f"Failed to delete blog {blog_id}: {str(e)}")
            raise InvalidBlogDataException(f"Failed to delete blog: {str(e)}")

    @staticmethod
    def get_all_blogs(author_id=None):
        try:
            if author_id:
                blogs = Blog.objects.filter(author_id=author_id).order_by('-date_created')
            else:
                blogs = Blog.objects.all().order_by('-date_created')

            return blogs

        except Exception as e:
            logger.error(f"Failed to retrieve blogs: {str(e)}")
            raise InvalidBlogDataException(f"Failed to retrieve blogs: {str(e)}")

    @staticmethod
    def get_blog_by_id(blog_id):
        try:
            blog = Blog.objects.select_related('author').get(id=blog_id)
            return blog
        except Blog.DoesNotExist:
            raise BlogNotFoundException(f"Blog with ID '{blog_id}' not found")
        except Exception as e:
            logger.error(f"Failed to retrieve blog {blog_id}: {str(e)}")
            raise InvalidBlogDataException(f"Failed to retrieve blog: {str(e)}")

    @staticmethod
    def get_blogs_by_author(author_id):
        try:
            try:
                User.objects.get(id=author_id)
            except User.DoesNotExist:
                raise InvalidBlogDataException(f"Author with ID '{author_id}' not found")

            blogs = Blog.objects.filter(author_id=author_id).order_by('-date_created')
            return blogs

        except Exception as e:
            if isinstance(e, InvalidBlogDataException):
                raise e
            logger.error(f"Failed to retrieve blogs for author {author_id}: {str(e)}")
            raise InvalidBlogDataException(f"Failed to retrieve blogs for author: {str(e)}")

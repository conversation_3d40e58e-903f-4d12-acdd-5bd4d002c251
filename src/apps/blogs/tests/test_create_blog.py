import uuid
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from ..models.models import Blog
from ..services.blog_service import BlogService
from ..exceptions.exceptions import InvalidBlogDataException

User = get_user_model()

class CreateBlogTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_blog_success(self):
        blog = BlogService.create_blog(
            title='Test Blog',
            content='This is a test blog content',
            author_id=self.user.id
        )
        
        self.assertIsInstance(blog, Blog)
        self.assertEqual(blog.title, 'Test Blog')
        self.assertEqual(blog.content, 'This is a test blog content')
        self.assertEqual(blog.author, self.user)

    def test_create_blog_invalid_author(self):
        with self.assertRaises(InvalidBlogDataException):
            BlogService.create_blog(
                title='Test Blog',
                content='This is a test blog content',
                author_id=uuid.uuid4()
            )

    def test_create_blog_empty_title(self):
        with self.assertRaises(InvalidBlogDataException):
            BlogService.create_blog(
                title='',
                content='This is a test blog content',
                author_id=self.user.id
            )

    def test_create_blog_empty_content(self):
        with self.assertRaises(InvalidBlogDataException):
            BlogService.create_blog(
                title='Test Blog',
                content='',
                author_id=self.user.id
            )


class CreateBlogAPITestCase(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_blog_api_success(self):
        data = {
            'title': 'Test Blog',
            'content': 'This is a test blog content',
            'author_id': str(self.user.id)
        }
        
        response = self.client.post('/api/blogs/create/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], 'Test Blog')
        self.assertEqual(response.data['content'], 'This is a test blog content')

    def test_create_blog_api_invalid_data(self):
        data = {
            'title': '',  # Empty title
            'content': 'This is a test blog content',
            'author_id': str(self.user.id)
        }
        
        response = self.client.post('/api/blogs/create/', data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

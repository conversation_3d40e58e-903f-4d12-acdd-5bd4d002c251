from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from ..models.models import Blog
from ..services.blog_service import BlogService
from ..exceptions.exceptions import BlogNotFoundException

User = get_user_model()

class GetBlogsTestCase(TestCase):
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='testuser1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )
        self.blog1 = Blog.objects.create(
            title='Blog 1',
            content='Content 1',
            author=self.user1
        )
        self.blog2 = Blog.objects.create(
            title='Blog 2',
            content='Content 2',
            author=self.user2
        )
        self.blog3 = Blog.objects.create(
            title='Blog 3',
            content='Content 3',
            author=self.user1
        )

    def test_get_all_blogs(self):
        blogs = BlogService.get_all_blogs()
        self.assertEqual(blogs.count(), 3)

    def test_get_blogs_by_author(self):
        blogs = BlogService.get_blogs_by_author(self.user1.id)
        self.assertEqual(blogs.count(), 2)
        
        blogs = BlogService.get_blogs_by_author(self.user2.id)
        self.assertEqual(blogs.count(), 1)

    def test_get_blog_by_id(self):
        blog = BlogService.get_blog_by_id(self.blog1.id)
        self.assertEqual(blog.title, 'Blog 1')
        self.assertEqual(blog.author, self.user1)

    def test_get_blog_by_invalid_id(self):
        import uuid
        with self.assertRaises(BlogNotFoundException):
            BlogService.get_blog_by_id(uuid.uuid4())


class GetBlogsAPITestCase(APITestCase):
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='testuser1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.blog1 = Blog.objects.create(
            title='Blog 1',
            content='Content 1',
            author=self.user1
        )
        self.blog2 = Blog.objects.create(
            title='Blog 2',
            content='Content 2',
            author=self.user2
        )

    def test_get_all_blogs_api(self):
        response = self.client.get('/api/blogs/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 2)
        self.assertEqual(len(response.data['blogs']), 2)

    def test_get_blog_by_id_api(self):
        response = self.client.get(f'/api/blogs/{self.blog1.id}/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['blog']['title'], 'Blog 1')

    def test_get_blogs_by_author_api(self):
        response = self.client.get(f'/api/blogs/?author_id={self.user1.id}')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)

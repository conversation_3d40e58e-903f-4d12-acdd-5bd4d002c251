# Blogs App

This Django app handles blog post management with a clean, organized structure following the same pattern as the users app.

## Structure

```
blogs/
├── __init__.py
├── admin.py                    # Django admin configuration
├── apps.py                     # App configuration
├── models.py                   # Model imports (imports from models/)
├── tests.py                    # Test imports (imports from tests/)
├── urls.py                     # URL routing
├── views.py                    # View imports (imports from views/)
├── README.md                   # This file
├── migrations/                 # Database migrations
├── models/                     # Model definitions
│   ├── __init__.py
│   └── models.py              # Blog model
├── serializers/               # DRF serializers
│   └── blog_serializers.py   # Blog serializers
├── services/                  # Business logic
│   ├── __init__.py
│   └── blog_service.py        # Blog service layer
├── views/                     # API views
│   ├── __init__.py
│   └── blog_views.py          # Blog views
├── tests/                     # Test cases
│   ├── __init__.py
│   ├── test_create_blog.py    # Blog creation tests
│   └── test_get_blogs.py      # Blog retrieval tests
├── exceptions/                # Custom exceptions
│   ├── __init__.py
│   └── exceptions.py          # Blog-specific exceptions
└── validators/                # Data validation
    └── blog_validators.py     # Blog validation functions
```

## Features

### Models
- **Blog**: Main blog post model with title, content, author, and image URL
- Inherits from BaseModel for common fields (id, created_at, updated_at)
- Foreign key relationship to User model

### API Endpoints
- `POST /api/blogs/create/` - Create a new blog post
- `POST /api/blogs/update/` - Update an existing blog post
- `GET /api/blogs/` - Get all blog posts (with optional author filter)
- `GET /api/blogs/<id>/` - Get a specific blog post by ID
- `GET /api/blogs/author/<author_id>/` - Get all blogs by a specific author
- `DELETE /api/blogs/delete/<id>/` - Delete a blog post

### Services
- **BlogService**: Contains all business logic for blog operations
  - `create_blog()` - Create new blog with validation
  - `update_blog()` - Update existing blog with permission checks
  - `delete_blog()` - Delete blog with permission checks
  - `get_all_blogs()` - Retrieve all blogs with optional filtering
  - `get_blog_by_id()` - Get specific blog by ID
  - `get_blogs_by_author()` - Get blogs by author

### Serializers
- **BlogCreateSerializer**: For creating new blog posts
- **BlogUpdateSerializer**: For updating existing blog posts
- **BlogListSerializer**: For listing blogs (with truncated content)
- **BlogDetailSerializer**: For detailed blog view

### Exceptions
- **BlogException**: Base exception for blog operations
- **BlogNotFoundException**: When blog is not found
- **BlogAlreadyExistsException**: When blog already exists
- **InvalidBlogDataException**: When blog data is invalid
- **BlogPermissionDeniedException**: When user lacks permissions
- **FileUploadException**: When file upload fails

### Validators
- **validate_blog_title()**: Validates blog title format and length
- **validate_blog_content()**: Validates blog content requirements
- **validate_image_url()**: Validates image URL format
- **BlogValidationMixin**: Mixin for adding validation to serializers

### Tests
- **CreateBlogTestCase**: Unit tests for blog creation
- **CreateBlogAPITestCase**: API tests for blog creation
- **GetBlogsTestCase**: Unit tests for blog retrieval
- **GetBlogsAPITestCase**: API tests for blog retrieval

## Usage

### Creating a Blog
```python
from apps.blogs.services.blog_service import BlogService

blog = BlogService.create_blog(
    title="My Blog Post",
    content="This is the content of my blog post",
    author_id=user.id,
    image=image_file  # Optional
)
```

### Getting Blogs
```python
# Get all blogs
all_blogs = BlogService.get_all_blogs()

# Get blogs by author
author_blogs = BlogService.get_blogs_by_author(author_id)

# Get specific blog
blog = BlogService.get_blog_by_id(blog_id)
```

### Updating a Blog
```python
updated_blog = BlogService.update_blog(
    blog_id=blog.id,
    title="Updated Title",
    content="Updated content",
    user_id=user.id  # For permission check
)
```

## Dependencies
- Django REST Framework
- PIL (for image handling)
- Common services (FileService for image uploads)
- Users app (for author relationship)

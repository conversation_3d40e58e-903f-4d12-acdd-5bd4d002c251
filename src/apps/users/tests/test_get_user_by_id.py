import json
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
import uuid

User = get_user_model()


class GetUserByIdTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        # Create an inactive user
        self.inactive_user = User.objects.create_user(
            username='inactiveuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Inactive',
            last_name='User',
            is_active=False
        )
        
        self.url = reverse('get_user_by_id', kwargs={'id': self.user.id})
        self.inactive_url = reverse('get_user_by_id', kwargs={'id': self.inactive_user.id})

    def test_get_user_by_id_success(self):
        """Test getting a user by valid ID"""
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('user', data)
        
        user_data = data['user']
        
        # Check user data
        self.assertEqual(user_data['id'], str(self.user.id))
        self.assertEqual(user_data['username'], 'testuser')
        self.assertEqual(user_data['email'], '<EMAIL>')
        self.assertEqual(user_data['first_name'], 'Test')
        self.assertEqual(user_data['last_name'], 'User')
        self.assertEqual(user_data['full_name'], 'Test User')
        self.assertTrue(user_data['is_active'])

    def test_get_inactive_user_by_id(self):
        """Test getting an inactive user by ID"""
        response = self.client.get(self.inactive_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        user_data = data['user']
        
        self.assertEqual(user_data['username'], 'inactiveuser')
        self.assertFalse(user_data['is_active'])

    def test_get_user_by_invalid_id(self):
        """Test getting a user with invalid UUID"""
        invalid_id = uuid.uuid4()
        invalid_url = reverse('get_user_by_id', kwargs={'id': invalid_id})

        response = self.client.get(invalid_url)

        # Should return 404 with custom error format
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Check the custom error response format
        data = response.json()
        self.assertIn('error', data)
        self.assertIn('code', data['error'])
        self.assertIn('message', data['error'])
        self.assertEqual(data['success'], False)

    def test_get_user_by_malformed_id(self):
        """Test getting a user with malformed UUID"""
        # This should return 404 due to URL pattern not matching
        response = self.client.get('/api/users/invalid-uuid/')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_user_data_structure(self):
        """Test that user data has the expected structure"""
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        user_data = data['user']
        
        # Check that all expected fields are present
        expected_fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'full_name', 'image_url', 'phone_number', 'date_joined',
            'date_updated', 'last_login', 'is_active', 'is_staff'
        ]
        
        for field in expected_fields:
            self.assertIn(field, user_data)
        
        # Check that sensitive fields are not included
        self.assertNotIn('password', user_data)
        self.assertNotIn('date_deleted', user_data)

    def test_user_detail_vs_list_serialization(self):
        """Test that user detail has more fields than user list"""
        # Get user detail
        detail_response = self.client.get(self.url)
        detail_data = detail_response.json()['user']
        
        # Get user list
        list_url = reverse('get_all_users')
        list_response = self.client.get(list_url)
        list_data = list_response.json()['users'][0]
        
        # Detail should have more fields
        self.assertIn('date_updated', detail_data)
        self.assertIn('last_login', detail_data)
        self.assertIn('is_staff', detail_data)
        
        # These fields might not be in the list view
        self.assertNotIn('date_updated', list_data)
        self.assertNotIn('last_login', list_data)
        self.assertNotIn('is_staff', list_data)

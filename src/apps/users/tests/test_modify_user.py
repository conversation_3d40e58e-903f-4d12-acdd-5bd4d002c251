import uuid
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model

User = get_user_model()


class ModifyUserTestCase(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.modify_url = reverse('modify')
        
        # Create a test user
        self.test_user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123',
            first_name='Test',
            last_name='User'
        )
        
        self.valid_modify_data = {
            'id': str(self.test_user.id),
            'first_name': 'Updated',
            'last_name': 'Name',
            'username': 'updateduser',
            'email': '<EMAIL>',
            'image_modifided': False
        }

    def test_modify_user_success(self):
        """Test successful user modification"""
        response = self.client.post(self.modify_url, self.valid_modify_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check response structure
        data = response.json()
        self.assertIn('id', data)
        self.assertIn('first_name', data)
        self.assertIn('last_name', data)
        self.assertIn('username', data)
        self.assertIn('email', data)
        self.assertIn('image_url', data)
        
        # Verify data was updated
        self.assertEqual(data['first_name'], 'Updated')
        self.assertEqual(data['last_name'], 'Name')
        self.assertEqual(data['username'], 'updateduser')
        self.assertEqual(data['email'], '<EMAIL>')
        
        # Verify user was updated in database
        updated_user = User.objects.get(id=self.test_user.id)
        self.assertEqual(updated_user.first_name, 'Updated')
        self.assertEqual(updated_user.last_name, 'Name')
        self.assertEqual(updated_user.username, 'updateduser')
        self.assertEqual(updated_user.email, '<EMAIL>')

    def test_modify_user_invalid_id(self):
        """Test modification with invalid user ID"""
        invalid_data = self.valid_modify_data.copy()
        invalid_data['id'] = str(uuid.uuid4())
        
        response = self.client.post(self.modify_url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Check error response format
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['success'], False)
        self.assertIn('USER_NOT_FOUND', data['error']['code'])

    def test_modify_user_missing_id(self):
        """Test modification without user ID"""
        invalid_data = self.valid_modify_data.copy()
        del invalid_data['id']

        response = self.client.post(self.modify_url, invalid_data, format='json')

        # Should return 400 for validation error (missing required field)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Check validation error response format
        data = response.json()
        self.assertIn('id', data)  # Field validation error

    def test_modify_user_duplicate_username(self):
        """Test modification with existing username"""
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='password123'
        )
        
        invalid_data = self.valid_modify_data.copy()
        invalid_data['username'] = 'otheruser'
        
        response = self.client.post(self.modify_url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_409_CONFLICT)

        # Check error response format
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['success'], False)
        self.assertIn('USER_ALREADY_EXISTS', data['error']['code'])

    def test_modify_user_duplicate_email(self):
        """Test modification with existing email"""
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='password123'
        )
        
        invalid_data = self.valid_modify_data.copy()
        invalid_data['email'] = '<EMAIL>'
        
        response = self.client.post(self.modify_url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_409_CONFLICT)

        # Check error response format
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['success'], False)
        self.assertIn('USER_ALREADY_EXISTS', data['error']['code'])

    def test_modify_user_missing_required_fields(self):
        """Test modification with missing required fields"""
        incomplete_data = {
            'id': str(self.test_user.id),
            'first_name': 'Updated'
            # Missing other required fields
        }
        
        response = self.client.post(self.modify_url, incomplete_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_modify_user_invalid_email_format(self):
        """Test modification with invalid email format"""
        invalid_data = self.valid_modify_data.copy()
        invalid_data['email'] = 'invalid-email'
        
        response = self.client.post(self.modify_url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_modify_user_empty_required_fields(self):
        """Test modification with empty required fields"""
        invalid_data = self.valid_modify_data.copy()
        invalid_data['username'] = ''
        
        response = self.client.post(self.modify_url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_response_data_structure(self):
        """Test that response has the expected data structure"""
        response = self.client.post(self.modify_url, self.valid_modify_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        
        # Check required fields
        required_fields = ['id', 'first_name', 'last_name', 'username', 'email', 'image_url']
        for field in required_fields:
            self.assertIn(field, data)
        
        # Check data types
        self.assertIsInstance(data['id'], str)
        self.assertIsInstance(data['first_name'], str)
        self.assertIsInstance(data['last_name'], str)
        self.assertIsInstance(data['username'], str)
        self.assertIsInstance(data['email'], str)
        
        # Verify UUID format
        try:
            uuid.UUID(data['id'])
        except ValueError:
            self.fail("id is not a valid UUID")

    def test_modify_user_same_username_email(self):
        """Test modification with same username and email (should succeed)"""
        same_data = {
            'id': str(self.test_user.id),
            'first_name': 'Updated',
            'last_name': 'Name',
            'username': self.test_user.username,  # Same username
            'email': self.test_user.email,        # Same email
            'image_modifided': False
        }
        
        response = self.client.post(self.modify_url, same_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify only first_name and last_name were updated
        updated_user = User.objects.get(id=self.test_user.id)
        self.assertEqual(updated_user.first_name, 'Updated')
        self.assertEqual(updated_user.last_name, 'Name')
        self.assertEqual(updated_user.username, self.test_user.username)
        self.assertEqual(updated_user.email, self.test_user.email)

import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

def validate_blog_title(title):
    if not title or not title.strip():
        raise ValidationError(_('Blog title cannot be empty or only whitespace.'))
    
    if len(title.strip()) < 5:
        raise ValidationError(_('Blog title must be at least 5 characters long.'))
    
    if len(title.strip()) > 200:
        raise ValidationError(_('Blog title cannot exceed 200 characters.'))
    
    # Check if title contains at least one alphanumeric character
    if not re.search(r'[a-zA-Z0-9]', title):
        raise ValidationError(_('Blog title must contain at least one alphanumeric character.'))

def validate_blog_content(content):
    if not content or not content.strip():
        raise ValidationError(_('Blog content cannot be empty or only whitespace.'))
    
    if len(content.strip()) < 10:
        raise ValidationError(_('Blog content must be at least 10 characters long.'))

def validate_image_url(url):
    if url and url.strip():
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(url):
            raise ValidationError(_('Please enter a valid URL.'))
        
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        if not any(url.lower().endswith(ext) for ext in image_extensions):
            raise ValidationError(_('URL must point to a valid image file (jpg, jpeg, png, gif, bmp, webp).'))

class BlogValidationMixin:
    def validate_title(self, value):
        validate_blog_title(value)
        return value
    
    def validate_content(self, value):
        validate_blog_content(value)
        return value
    
    def validate_image_url(self, value):
        if value:
            validate_image_url(value)
        return value

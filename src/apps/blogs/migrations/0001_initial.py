# Generated by Django 5.2.6 on 2025-09-11 11:14

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Blog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('date_created', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('date_updated', models.DateTimeField(auto_now=True)),
                ('date_deleted', models.DateTimeField(blank=True, null=True)),
                ('title', models.CharField(help_text='Enter the title of the blog post', max_length=200, verbose_name='Blog Title')),
                ('content', models.TextField(help_text='Enter the main content of the blog post', verbose_name='Content')),
                ('image_url', models.CharField(blank=True, help_text='URL of the blog post image', max_length=500, null=True, verbose_name='blog image')),
                ('author', models.ForeignKey(help_text='The user who wrote this blog post', on_delete=django.db.models.deletion.CASCADE, related_name='blogs', to=settings.AUTH_USER_MODEL, verbose_name='Author')),
            ],
            options={
                'verbose_name': 'Blog',
                'verbose_name_plural': 'Blogs',
                'db_table': 'blogs_blog',
                'ordering': ['-date_created'],
            },
        ),
    ]

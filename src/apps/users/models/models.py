from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from django.core.validators import RegexValidator
import uuid

class User(AbstractUser):
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    image_url = models.CharField(max_length=200, null=True, blank=True)
    phone_number = models.CharField(
        validators=[phone_regex],
        max_length=17,
        null=False,
        blank=False,
        help_text="Phone number in international format"
    )
    date_deleted = models.DateTimeField(null=True, blank=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'users'
        db_table = 'users_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.username} ({self.email})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def is_deleted(self):
        return self.date_deleted is not None


    @classmethod
    def create_user(cls,username,email,password):
        user = cls(username=username,email=email)
        user.set_password(password)
        return user

    def modify_user(self, **kwargs):
        allowed_fields = [
            'first_name', 'last_name', 'username', 'email',
            'phone_number', 'image_url'
        ]
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(self, field):
                setattr(self, field, value)
            elif field not in allowed_fields:
                raise ValueError(f"Field '{field}' is not allowed to be modified")
        self.date_updated = timezone.now()
        return self

    
    def soft_delete(self):
        if self.date_deleted is not None:
            raise ValueError("User is already deleted")
        self.date_deleted = timezone.now()
        self.is_active = False 
        self.save()
        return self

    def restore(self):
        if self.date_deleted is None:
            raise ValueError("User is not deleted")

        self.date_deleted = None
        self.is_active = True
        return self

    def hard_delete(self):
        super().delete()
from django.contrib import admin
from .models.models import Blog

@admin.register(Blog)
class BlogAdmin(admin.ModelAdmin):
    list_display = ['title', 'author', 'date_created', 'date_updated']
    list_filter = ['date_created', 'date_updated', 'author']
    search_fields = ['title', 'content', 'author__username']
    readonly_fields = ['date_created', 'date_updated']
    ordering = ['-date_created']

    fieldsets = (
        (None, {
            'fields': ('title', 'content', 'author')
        }),
        ('Media', {
            'fields': ('image_url',)
        }),
        ('Timestamps', {
            'fields': ('date_created', 'date_updated'),
            'classes': ('collapse',)
        }),
    )

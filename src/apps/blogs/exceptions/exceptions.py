from rest_framework import status
from rest_framework.views import exception_handler
from rest_framework.response import Response
import logging

logger = logging.getLogger(__name__)

class BlogException(Exception):
    def __init__(self, message="Blog operation failed", code="BLOG_ERROR", status_code=status.HTTP_400_BAD_REQUEST):
        self.message = message
        self.code = code
        self.status_code = status_code
        super().__init__(self.message)

class BlogNotFoundException(BlogException):
    def __init__(self, message="Blog not found", code="BLOG_NOT_FOUND"):
        super().__init__(message, code, status.HTTP_404_NOT_FOUND)

class BlogAlreadyExistsException(BlogException):
    def __init__(self, message="Blog already exists", code="BLOG_ALREADY_EXISTS"):
        super().__init__(message, code, status.HTTP_409_CONFLICT)

class InvalidBlogDataException(BlogException):
    def __init__(self, message="Invalid blog data", code="INVALID_BLOG_DATA"):
        super().__init__(message, code, status.HTTP_400_BAD_REQUEST)

class BlogPermissionDeniedException(BlogException):
    def __init__(self, message="Permission denied for blog operation", code="BLOG_PERMISSION_DENIED"):
        super().__init__(message, code, status.HTTP_403_FORBIDDEN)

class FileUploadException(BlogException):
    def __init__(self, message="File upload failed", code="FILE_UPLOAD_ERROR"):
        super().__init__(message, code, status.HTTP_400_BAD_REQUEST)

def blog_exception_handler(exc, context):
    if isinstance(exc, BlogException):
        logger.error(f"Blog exception occurred: {exc.message} (Code: {exc.code})")
        return Response({
            'error': {
                'message': exc.message,
                'code': exc.code,
                'status_code': exc.status_code
            }
        }, status=exc.status_code)
    return exception_handler(exc, context)

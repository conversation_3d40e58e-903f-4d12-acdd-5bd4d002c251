import logging
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>ars<PERSON>
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample
from ..models.models import Blog
from ..exceptions.exceptions import BlogException
from ..serializers.blog_serializers import (
    BlogCreateSerializer,
    BlogUpdateSerializer,
    BlogListSerializer,
    BlogDetailSerializer
)
from ..services.blog_service import BlogService

User = get_user_model()
logger = logging.getLogger(__name__)


class CreateBlogView(generics.CreateAPIView):
    parser_classes = (FormParser, MultiPartParser,JSONParser)
    queryset = Blog.objects.all()
    serializer_class = BlogCreateSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        blog = serializer.save()
        headers = self.get_success_headers(serializer.data)

        logger.info(f"Blog created successfully: {blog.title}")

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )


class UpdateBlogView(generics.GenericAPIView):
    parser_classes = (FormParser, MultiPartParser, JSONParser)
    queryset = Blog.objects.all()
    serializer_class = BlogUpdateSerializer
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data

        blog = BlogService.update_blog(
            blog_id=validated_data['id'],
            title=validated_data['title'],
            content=validated_data['content'],
            image_modified=validated_data.get('image_modified', False),
            image=validated_data.get('image'),
            user_id=request.user.id if hasattr(request, 'user') and request.user.is_authenticated else None
        )

        response_serializer = self.get_serializer(blog)

        logger.info(f"Blog updated successfully: {blog.title}")

        return Response(
            response_serializer.data,
            status=status.HTTP_200_OK
        )


class GetAllBlogsView(generics.ListAPIView):
    serializer_class = BlogListSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        author_id = self.request.query_params.get('author_id')
        return BlogService.get_all_blogs(author_id=author_id)

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            logger.info(f"Retrieved {len(serializer.data)} blogs")

            return Response({
                'count': len(serializer.data),
                'blogs': serializer.data
            }, status=status.HTTP_200_OK)

        except BlogException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting all blogs: {str(e)}")
            raise BlogException(
                message="An unexpected error occurred while retrieving blogs",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    pagination_class = None


class GetBlogByIdView(generics.RetrieveAPIView):
    serializer_class = BlogDetailSerializer
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        blog_id = self.kwargs.get('id')
        return BlogService.get_blog_by_id(blog_id)

    def get(self, request, *args, **kwargs):
        blog = self.get_object()
        serializer = self.get_serializer(blog)
        return Response({
            'blog': serializer.data
        }, status=status.HTTP_200_OK)


class GetBlogsByAuthorView(generics.ListAPIView):
    serializer_class = BlogListSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        author_id = self.kwargs.get('author_id')
        return BlogService.get_blogs_by_author(author_id)

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            logger.info(f"Retrieved {len(serializer.data)} blogs for author")

            return Response({
                'count': len(serializer.data),
                'blogs': serializer.data
            }, status=status.HTTP_200_OK)

        except BlogException as e:
            raise e
        except Exception as e:
            logger.error(f"Unexpected error in getting blogs by author: {str(e)}")
            raise BlogException(
                message="An unexpected error occurred while retrieving blogs",
                code="INTERNAL_ERROR",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    pagination_class = None


class DeleteBlogView(generics.DestroyAPIView):
    permission_classes = [AllowAny]
    lookup_field = 'id'

    def get_object(self):
        blog_id = self.kwargs.get('id')
        return BlogService.get_blog_by_id(blog_id)

    def delete(self, request, *args, **kwargs):
        blog_id = self.kwargs.get('id')
        user_id = request.user.id if hasattr(request, 'user') and request.user.is_authenticated else None
        
        BlogService.delete_blog(blog_id, user_id)
        
        logger.info(f"Blog deleted successfully: {blog_id}")
        
        return Response({
            'message': 'Blog deleted successfully'
        }, status=status.HTTP_204_NO_CONTENT)
